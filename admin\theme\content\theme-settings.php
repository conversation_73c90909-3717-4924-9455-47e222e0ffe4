<?php
/**
 * Theme Settings Content Template
 * Organized settings with tabs for different categories
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure settings variable is available
if (!isset($settings)) {
    $settings = [];
}
?>

<div class="admin-tabs">
    <!-- Tab Navigation -->
    <div class="tabs">
        <button class="tab active" onclick="showTab('general-settings')">
            <i class="fas fa-cog me-2"></i>
            General Settings
        </button>
        <button class="tab" onclick="showTab('colors-branding')">
            <i class="fas fa-palette me-2"></i>
            Colors & Branding
        </button>
        <button class="tab" onclick="showTab('social-media')">
            <i class="fas fa-share-alt me-2"></i>
            Social Media
        </button>
        <button class="tab" onclick="showTab('footer-content')">
            <i class="fas fa-align-left me-2"></i>
            Footer Content
        </button>
        <button class="tab" onclick="showTab('contact-page')">
            <i class="fas fa-envelope me-2"></i>
            Contact Page
        </button>
    </div>

    <!-- General Settings Tab -->
    <div id="general-settings" class="tab-content active">
        <form method="POST" enctype="multipart/form-data" class="form-section">
            <input type="hidden" name="action" value="update_general">
            
            <h3 class="form-section-title">
                <i class="fas fa-info-circle me-2"></i>
                General Website Settings
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="site_name">Site Name</label>
                    <input type="text" id="site_name" name="site_name" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="site_tagline">Site Tagline</label>
                    <input type="text" id="site_tagline" name="site_tagline" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['site_tagline'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="phone_number">Phone Number</label>
                    <input type="text" id="phone_number" name="phone_number" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['phone_number'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['email'] ?? ''); ?>">
                </div>
            </div>
            
            <div class="form-group">
                <label for="address">Business Address</label>
                <textarea id="address" name="address" class="form-control" rows="3"><?php echo htmlspecialchars($settings['address'] ?? ''); ?></textarea>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save General Settings
            </button>
        </form>
    </div>

    <!-- Colors & Branding Tab -->
    <div id="colors-branding" class="tab-content">
        <form method="POST" enctype="multipart/form-data" class="form-section">
            <input type="hidden" name="action" value="update_colors">
            
            <h3 class="form-section-title">
                <i class="fas fa-palette me-2"></i>
                Colors & Branding
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="primary_color">Primary Color</label>
                    <div class="color-input-group">
                        <input type="color" id="primary_color_picker" class="color-picker"
                               value="<?php echo $settings['primary_color'] ?? '#1A1A1A'; ?>">
                        <input type="text" id="primary_color" name="primary_color" class="form-control color-text"
                               value="<?php echo $settings['primary_color'] ?? '#1A1A1A'; ?>"
                               onchange="document.getElementById('primary_color_picker').value = this.value">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="accent_color">Accent Color</label>
                    <div class="color-input-group">
                        <input type="color" id="accent_color_picker" class="color-picker"
                               value="<?php echo $settings['accent_color'] ?? '#E67E22'; ?>"
                               onchange="document.getElementById('accent_color').value = this.value">
                        <input type="text" id="accent_color" name="accent_color" class="form-control color-text"
                               value="<?php echo $settings['accent_color'] ?? '#E67E22'; ?>"
                               onchange="document.getElementById('accent_color_picker').value = this.value">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="secondary_color">Secondary Color</label>
                    <div class="color-input-group">
                        <input type="color" id="secondary_color_picker" class="color-picker"
                               value="<?php echo $settings['secondary_color'] ?? '#F5F5F5'; ?>"
                               onchange="document.getElementById('secondary_color').value = this.value">
                        <input type="text" id="secondary_color" name="secondary_color" class="form-control color-text"
                               value="<?php echo $settings['secondary_color'] ?? '#F5F5F5'; ?>"
                               onchange="document.getElementById('secondary_color_picker').value = this.value">
                    </div>
                </div>
            </div>

            <h4 class="mt-4 mb-3">Table Appearance</h4>
            <div class="form-grid">
                <div class="form-group">
                    <label for="table_header_bg">Table Header Background</label>
                    <div class="color-input-group">
                        <input type="color" id="table_header_bg_picker" class="color-picker"
                               value="<?php echo $settings['table_header_bg'] ?? '#f8f9fa'; ?>"
                               onchange="document.getElementById('table_header_bg').value = this.value">
                        <input type="text" id="table_header_bg" name="table_header_bg" class="form-control color-text"
                               value="<?php echo $settings['table_header_bg'] ?? '#f8f9fa'; ?>"
                               onchange="document.getElementById('table_header_bg_picker').value = this.value">
                    </div>
                </div>

                <div class="form-group">
                    <label for="table_header_text">Table Header Text Color</label>
                    <div class="color-input-group">
                        <input type="color" id="table_header_text_picker" class="color-picker"
                               value="<?php echo $settings['table_header_text'] ?? '#495057'; ?>"
                               onchange="document.getElementById('table_header_text').value = this.value">
                        <input type="text" id="table_header_text" name="table_header_text" class="form-control color-text"
                               value="<?php echo $settings['table_header_text'] ?? '#495057'; ?>"
                               onchange="document.getElementById('table_header_text_picker').value = this.value">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="logo_file">Site Logo</label>
                        <input type="file" id="logo_file" name="logo_file" class="form-control" accept=".jpg,.jpeg,.png,.svg">
                        <small class="text-muted">Current: <?php echo basename($settings['site_logo'] ?? 'No logo uploaded'); ?></small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="favicon_file">Favicon</label>
                        <input type="file" id="favicon_file" name="favicon_file" class="form-control" accept=".ico,.png">
                        <small class="text-muted">Current: <?php echo basename($settings['favicon'] ?? 'No favicon uploaded'); ?></small>
                    </div>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Colors & Branding
            </button>
        </form>
    </div>

    <!-- Social Media Tab -->
    <div id="social-media" class="tab-content">
        <form method="POST" class="form-section">
            <input type="hidden" name="action" value="update_social">
            
            <h3 class="form-section-title">
                <i class="fas fa-share-alt me-2"></i>
                Social Media Links
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="facebook_url">
                        <i class="fab fa-facebook me-2"></i>
                        Facebook URL
                    </label>
                    <input type="url" id="facebook_url" name="facebook_url" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['facebook_url'] ?? ''); ?>" 
                           placeholder="https://facebook.com/yourpage">
                </div>
                
                <div class="form-group">
                    <label for="twitter_url">
                        <i class="fab fa-twitter me-2"></i>
                        Twitter URL
                    </label>
                    <input type="url" id="twitter_url" name="twitter_url" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['twitter_url'] ?? ''); ?>" 
                           placeholder="https://twitter.com/yourhandle">
                </div>
                
                <div class="form-group">
                    <label for="linkedin_url">
                        <i class="fab fa-linkedin me-2"></i>
                        LinkedIn URL
                    </label>
                    <input type="url" id="linkedin_url" name="linkedin_url" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['linkedin_url'] ?? ''); ?>" 
                           placeholder="https://linkedin.com/company/yourcompany">
                </div>
                
                <div class="form-group">
                    <label for="instagram_url">
                        <i class="fab fa-instagram me-2"></i>
                        Instagram URL
                    </label>
                    <input type="url" id="instagram_url" name="instagram_url" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['instagram_url'] ?? ''); ?>" 
                           placeholder="https://instagram.com/yourhandle">
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Social Media Links
            </button>
        </form>
    </div>

    <!-- Footer Content Tab -->
    <div id="footer-content" class="tab-content">
        <form method="POST" class="form-section">
            <input type="hidden" name="action" value="update_footer">
            
            <h3 class="form-section-title">
                <i class="fas fa-align-left me-2"></i>
                Footer Content
            </h3>
            
            <div class="form-group">
                <label for="footer_about_text">About Text</label>
                <textarea id="footer_about_text" name="footer_about_text" class="form-control" rows="4" 
                          placeholder="Brief description about your company..."><?php echo htmlspecialchars($settings['footer_about_text'] ?? ''); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="footer_copyright">Copyright Text</label>
                <input type="text" id="footer_copyright" name="footer_copyright" class="form-control" 
                       value="<?php echo htmlspecialchars($settings['footer_copyright'] ?? ''); ?>" 
                       placeholder="All rights reserved.">
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Footer Content
            </button>
        </form>
    </div>

    <!-- Contact Page Tab -->
    <div id="contact-page" class="tab-content">
        <form method="POST" enctype="multipart/form-data" class="form-section">
            <input type="hidden" name="action" value="update_contact">
            
            <h3 class="form-section-title">
                <i class="fas fa-envelope me-2"></i>
                Contact Page Settings
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="contact_hero_title">Hero Section Title</label>
                    <input type="text" id="contact_hero_title" name="contact_hero_title" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['contact_hero_title'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="contact_form_title">Contact Form Title</label>
                    <input type="text" id="contact_form_title" name="contact_form_title" class="form-control" 
                           value="<?php echo htmlspecialchars($settings['contact_form_title'] ?? ''); ?>">
                </div>
            </div>
            
            <div class="form-group">
                <label for="contact_hero_description">Hero Section Description</label>
                <textarea id="contact_hero_description" name="contact_hero_description" class="form-control" rows="3"><?php echo htmlspecialchars($settings['contact_hero_description'] ?? ''); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="business_hours">Business Hours</label>
                <textarea id="business_hours" name="business_hours" class="form-control" rows="4"><?php echo htmlspecialchars($settings['business_hours'] ?? ''); ?></textarea>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Contact Page Settings
            </button>
        </form>
    </div>
</div>
